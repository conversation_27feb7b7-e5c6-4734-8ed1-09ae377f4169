import pygame
import colorsys

class ColorSystem:
    """System do zarządzania kolorami samochodów z dynamicznym kolorowaniem"""
    
    # Paleta kolorów - każdy kolor ma nazwę i wartość RGB
    COLOR_PALETTE = {
        "0": {"name": "Czerwony", "rgb": (220, 20, 20), "hue_shift": 0},
        "1": {"name": "Niebies<PERSON>", "rgb": (20, 100, 220), "hue_shift": 240},
        "2": {"name": "<PERSON><PERSON><PERSON>", "rgb": (20, 180, 20), "hue_shift": 120},
        "3": {"name": "<PERSON><PERSON><PERSON><PERSON>", "rgb": (255, 220, 0), "hue_shift": 60},
        "4": {"name": "<PERSON>ole<PERSON>wy", "rgb": (150, 20, 200), "hue_shift": 280},
        "5": {"name": "Pomarańczowy", "rgb": (255, 140, 0), "hue_shift": 30},
        "6": {"name": "<PERSON><PERSON><PERSON>ow<PERSON>", "rgb": (255, 100, 150), "hue_shift": 330},
        "7": {"name": "Turkusowy", "rgb": (0, 200, 200), "hue_shift": 180},
        "8": {"name": "Biały", "rgb": (240, 240, 240), "hue_shift": 0},
        "9": {"name": "Czarny", "rgb": (40, 40, 40), "hue_shift": 0},
        "10": {"name": "Srebrny", "rgb": (180, 180, 180), "hue_shift": 0},
        "11": {"name": "Złoty", "rgb": (255, 215, 0), "hue_shift": 50}
    }
    
    @staticmethod
    def get_color_info(color_index):
        """Zwraca informacje o kolorze na podstawie indeksu"""
        return ColorSystem.COLOR_PALETTE.get(str(color_index), ColorSystem.COLOR_PALETTE["0"])
    
    @staticmethod
    def get_color_rgb(color_index):
        """Zwraca wartość RGB koloru"""
        color_info = ColorSystem.get_color_info(color_index)
        return color_info["rgb"]
    
    @staticmethod
    def get_color_name(color_index):
        """Zwraca nazwę koloru"""
        color_info = ColorSystem.get_color_info(color_index)
        return color_info["name"]
    
    @staticmethod
    def get_available_colors():
        """Zwraca listę dostępnych kolorów"""
        return list(ColorSystem.COLOR_PALETTE.keys())
    
    @staticmethod
    def colorize_surface(surface, target_color, method="multiply"):
        """
        Koloruje powierzchnię pygame na określony kolor
        
        Args:
            surface: pygame.Surface do pokolorowania
            target_color: tuple (R, G, B) docelowego koloru
            method: metoda kolorowania ("multiply", "overlay", "hue_shift")
        
        Returns:
            pygame.Surface: pokolorowana powierzchnia
        """
        if surface is None:
            return None
            
        # Stwórz kopię powierzchni
        colored_surface = surface.copy()
        
        if method == "multiply":
            # Metoda mnożenia - zachowuje cienie i detale
            color_overlay = pygame.Surface(surface.get_size())
            color_overlay.fill(target_color)
            colored_surface.blit(color_overlay, (0, 0), special_flags=pygame.BLEND_MULT)
            
        elif method == "overlay":
            # Metoda nakładki - bardziej intensywne kolory
            color_overlay = pygame.Surface(surface.get_size())
            color_overlay.fill(target_color)
            colored_surface.blit(color_overlay, (0, 0), special_flags=pygame.BLEND_OVERLAY)
            
        elif method == "hue_shift":
            # Zmiana odcienia - zachowuje jasność i nasycenie
            colored_surface = ColorSystem._hue_shift_surface(surface, target_color)
            
        return colored_surface
    
    @staticmethod
    def _hue_shift_surface(surface, target_color):
        """Zmienia odcień powierzchni zachowując jasność i nasycenie"""
        # Konwertuj docelowy kolor na HSV
        target_r, target_g, target_b = [c/255.0 for c in target_color]
        target_h, target_s, target_v = colorsys.rgb_to_hsv(target_r, target_g, target_b)
        
        # Stwórz kopię powierzchni
        colored_surface = surface.copy()
        
        # Pobierz tablicę pikseli
        pixel_array = pygame.surfarray.array3d(colored_surface)
        
        # Przetwórz każdy piksel
        for x in range(pixel_array.shape[0]):
            for y in range(pixel_array.shape[1]):
                r, g, b = pixel_array[x, y]
                
                # Pomiń przezroczyste piksele (czarne)
                if r == 0 and g == 0 and b == 0:
                    continue
                
                # Konwertuj na HSV
                r_norm, g_norm, b_norm = r/255.0, g/255.0, b/255.0
                h, s, v = colorsys.rgb_to_hsv(r_norm, g_norm, b_norm)
                
                # Zastąp odcień docelowym, zachowaj nasycenie i jasność
                new_h = target_h
                new_s = s * target_s  # Moduluj nasycenie
                new_v = v * target_v  # Moduluj jasność
                
                # Konwertuj z powrotem na RGB
                new_r, new_g, new_b = colorsys.hsv_to_rgb(new_h, new_s, new_v)
                pixel_array[x, y] = [int(new_r * 255), int(new_g * 255), int(new_b * 255)]
        
        # Zastosuj zmiany
        pygame.surfarray.blit_array(colored_surface, pixel_array)
        
        return colored_surface
    
    @staticmethod
    def load_and_colorize_car_image(car_name, color_index, size=(180, 120)):
        """
        Ładuje i koloruje grafikę samochodu
        
        Args:
            car_name: nazwa samochodu (np. "Xtreme", "future", "old")
            color_index: indeks koloru z palety
            size: tuple (width, height) docelowego rozmiaru
        
        Returns:
            pygame.Surface: pokolorowana grafika samochodu
        """
        try:
            # Ładuj bazową grafikę samochodu
            image_path = f"assets/img/cars/{car_name}_car.png"
            base_image = pygame.image.load(image_path)
            
            # Skaluj do odpowiedniego rozmiaru
            base_image = pygame.transform.scale(base_image, size)
            
            # Pobierz kolor z palety
            target_color = ColorSystem.get_color_rgb(color_index)
            
            # Koloruj grafikę
            colored_image = ColorSystem.colorize_surface(base_image, target_color, method="multiply")
            
            return colored_image
            
        except Exception as e:
            print(f"Błąd podczas ładowania i kolorowania grafiki samochodu: {e}")
            # Zwróć placeholder w przypadku błędu
            placeholder = pygame.Surface(size)
            placeholder.fill((100, 100, 100))
            return placeholder
    
    @staticmethod
    def create_color_swatch(color_index, size=(40, 40)):
        """
        Tworzy próbkę koloru do wyświetlenia w interfejsie
        
        Args:
            color_index: indeks koloru z palety
            size: tuple (width, height) rozmiaru próbki
        
        Returns:
            pygame.Surface: próbka koloru
        """
        swatch = pygame.Surface(size)
        color_rgb = ColorSystem.get_color_rgb(color_index)
        swatch.fill(color_rgb)
        
        # Dodaj ramkę
        pygame.draw.rect(swatch, (200, 200, 200), swatch.get_rect(), 2)
        
        return swatch
